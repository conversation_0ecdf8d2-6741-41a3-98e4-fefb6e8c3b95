#!/usr/bin/env python3
"""
Jednoduchý test script pro testování konverze dokumentů
"""
import requests
import base64
import json
from requests.auth import HTTPBasicAuth

def test_document_conversion():
    # Načtení skutečného DOCX souboru
    try:
        with open('test_real_docx_base64.txt', 'r') as f:
            base64_content = f.read().strip()
        test_content_size = len(base64.b64decode(base64_content))
    except FileNotFoundError:
        print("❌ Soubor test_real_docx_base64.txt nenalezen. Spusťte nejprve create_real_docx.py")
        return False
    
    # API endpoint
    url = "http://localhost:5041/api/v1/documentconversion"
    
    # Test data
    payload = {
        "name": "test.docx",
        "content": base64_content
    }
    
    # Basic auth credentials
    auth = HTTPBasicAuth('admin', '12345')
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    print("🚀 Testování konverze dokumentu...")
    print(f"📄 Soubor: {payload['name']}")
    print(f"📊 Velikost obsahu: {test_content_size} bytes")
    
    try:
        # Odeslání požadavku
        response = requests.post(url, json=payload, auth=auth, headers=headers, timeout=60)
        
        print(f"📡 HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Konverze úspěšná!")
            print(f"📝 Výstupní soubor: {result.get('name', 'N/A')}")
            print(f"📊 Velikost PDF: {len(base64.b64decode(result.get('content', '')))} bytes")
            return True
        else:
            print("❌ Konverze selhala!")
            print(f"🔍 Odpověď: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout - konverze trvá příliš dlouho")
        return False
    except requests.exceptions.RequestException as e:
        print(f"🔌 Chyba připojení: {e}")
        return False
    except Exception as e:
        print(f"💥 Neočekávaná chyba: {e}")
        return False

def test_health_endpoint():
    """Test health endpoint"""
    url = "http://localhost:5041/api/v1/health"
    auth = HTTPBasicAuth('admin', '12345')
    
    print("\n🏥 Testování health endpoint...")
    
    try:
        response = requests.get(url, auth=auth, timeout=10)
        print(f"📡 HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Health check úspěšný!")
            print(f"🔧 Služba: {result.get('service', 'N/A')}")
            print(f"💚 Status: {result.get('status', 'N/A')}")
            return True
        else:
            print("❌ Health check selhal!")
            return False
            
    except Exception as e:
        print(f"💥 Chyba: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Spouštění testů pro ProcessPoint.GraphApiServices")
    print("=" * 50)
    
    # Test health endpoint
    health_ok = test_health_endpoint()
    
    # Test document conversion
    conversion_ok = test_document_conversion()
    
    print("\n" + "=" * 50)
    print("📊 Výsledky testů:")
    print(f"🏥 Health endpoint: {'✅ OK' if health_ok else '❌ FAIL'}")
    print(f"📄 Document conversion: {'✅ OK' if conversion_ok else '❌ FAIL'}")
    
    if health_ok and conversion_ok:
        print("\n🎉 Všechny testy prošly úspěšně!")
        exit(0)
    else:
        print("\n💥 Některé testy selhaly!")
        exit(1)
