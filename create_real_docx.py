#!/usr/bin/env python3
"""
Vytvoření skutečného DOCX souboru pro testování
"""
import zipfile
import io
import base64

def create_minimal_docx():
    """Vyt<PERSON><PERSON>í minimální ale validní DOCX soubor"""
    
    # Vytvoření in-memory ZIP souboru
    docx_buffer = io.BytesIO()
    
    with zipfile.ZipFile(docx_buffer, 'w', zipfile.ZIP_DEFLATED) as docx:
        # [Content_Types].xml
        content_types = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>'''
        docx.writestr('[Content_Types].xml', content_types)
        
        # _rels/.rels
        rels = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>'''
        docx.writestr('_rels/.rels', rels)
        
        # word/document.xml
        document = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:body>
        <w:p>
            <w:r>
                <w:t>Test document for ProcessPoint.GraphApiServices conversion</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>This is a minimal but valid DOCX document.</w:t>
            </w:r>
        </w:p>
    </w:body>
</w:document>'''
        docx.writestr('word/document.xml', document)
    
    docx_buffer.seek(0)
    return docx_buffer.getvalue()

if __name__ == "__main__":
    print("📄 Vytváření skutečného DOCX souboru...")
    
    # Vytvoření DOCX
    docx_data = create_minimal_docx()
    
    # Uložení do souboru
    with open('test_real.docx', 'wb') as f:
        f.write(docx_data)
    
    # Vytvoření base64 pro API test
    base64_content = base64.b64encode(docx_data).decode('utf-8')
    
    print(f"✅ DOCX soubor vytvořen!")
    print(f"📊 Velikost: {len(docx_data)} bytes")
    print(f"🔤 Base64 délka: {len(base64_content)} znaků")
    
    # Uložení base64 do souboru pro snadné použití
    with open('test_real_docx_base64.txt', 'w') as f:
        f.write(base64_content)
    
    print("💾 Base64 obsah uložen do test_real_docx_base64.txt")
    print("🧪 Můžete nyní použít tento obsah pro testování API")
