#!/usr/bin/env python3
"""
Test s poškozeným DOCX souborem
"""
import requests
import base64
import json
from requests.auth import HTTPBasicAuth

def test_corrupted_document():
    # Vytvoření poškozené DOCX (nevalidní ZIP struktura)
    corrupted_content = b"PK\x03\x04\x14\x00\x00\x00\x08\x00" + b"CORRUPTED_DATA" * 50
    base64_content = base64.b64encode(corrupted_content).decode('utf-8')
    
    # API endpoint
    url = "http://localhost:5041/api/v1/documentconversion"
    
    # Test data
    payload = {
        "name": "corrupted.docx",
        "content": base64_content
    }
    
    # Basic auth credentials
    auth = HTTPBasicAuth('admin', '12345')
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    print("🧪 Testování s poškozeným DOCX souborem...")
    print(f"📄 Soubor: {payload['name']}")
    print(f"📊 Velikost obsahu: {len(corrupted_content)} bytes")
    
    try:
        # Odeslání požadavku
        response = requests.post(url, json=payload, auth=auth, headers=headers, timeout=60)
        
        print(f"📡 HTTP Status: {response.status_code}")
        
        if response.status_code == 400:
            result = response.json()
            print("✅ Chyba správně detekována!")
            print(f"🔍 Chybová zpráva: {result.get('error', 'N/A')}")
            return True
        elif response.status_code == 200:
            print("⚠️  Neočekávaně úspěšné - možná se poškození opravilo")
            return True
        else:
            print("❌ Neočekávaný status kód!")
            print(f"🔍 Odpověď: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout")
        return False
    except Exception as e:
        print(f"💥 Chyba: {e}")
        return False

def test_invalid_format():
    # Test s nevalidním formátem (obyčejný text jako DOCX)
    invalid_content = b"This is just plain text, not a DOCX file!"
    base64_content = base64.b64encode(invalid_content).decode('utf-8')
    
    payload = {
        "name": "invalid.docx",
        "content": base64_content
    }
    
    url = "http://localhost:5041/api/v1/documentconversion"
    auth = HTTPBasicAuth('admin', '12345')
    headers = {'Content-Type': 'application/json'}
    
    print("\n🧪 Testování s nevalidním formátem...")
    print(f"📄 Soubor: {payload['name']}")
    
    try:
        response = requests.post(url, json=payload, auth=auth, headers=headers, timeout=60)
        print(f"📡 HTTP Status: {response.status_code}")
        
        if response.status_code == 400:
            result = response.json()
            print("✅ Nevalidní formát správně detekován!")
            print(f"🔍 Chybová zpráva: {result.get('error', 'N/A')}")
            return True
        else:
            print("❌ Neočekávaný výsledek")
            return False
            
    except Exception as e:
        print(f"💥 Chyba: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testování error handling s poškozenými soubory")
    print("=" * 60)
    
    # Test poškozený DOCX
    corrupted_ok = test_corrupted_document()
    
    # Test nevalidní formát
    invalid_ok = test_invalid_format()
    
    print("\n" + "=" * 60)
    print("📊 Výsledky testů:")
    print(f"💥 Poškozený DOCX: {'✅ OK' if corrupted_ok else '❌ FAIL'}")
    print(f"🚫 Nevalidní formát: {'✅ OK' if invalid_ok else '❌ FAIL'}")
    
    if corrupted_ok and invalid_ok:
        print("\n🎉 Error handling funguje správně!")
        exit(0)
    else:
        print("\n💥 Některé testy selhaly!")
        exit(1)
