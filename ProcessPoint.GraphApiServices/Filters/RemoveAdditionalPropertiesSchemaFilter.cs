using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace ProcessPoint.GraphApiServices.Filters;

/// <summary>
/// Schema filter pro odstranění "additionalProperties": false z OpenAPI dokumentace
/// </summary>
public class RemoveAdditionalPropertiesSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        // Odstranění additionalProperties: false ze všech schémat
        schema.AdditionalProperties = null;
        schema.AdditionalPropertiesAllowed = true;
    }
}
