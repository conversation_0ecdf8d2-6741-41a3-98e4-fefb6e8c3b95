using Microsoft.AspNetCore.Mvc;
using ProcessPoint.GraphApiServices.Services;
using System.ComponentModel.DataAnnotations;

namespace ProcessPoint.GraphApiServices.Endpoints;

// Helper class for logger generic type parameter
internal sealed class DocumentConversionEndpointsLogger { }

public static class DocumentConversionEndpoints
{
    public static void MapDocumentConversionEndpoints(this IEndpointRouteBuilder endpoints)
    {
        var group = endpoints.MapGroup("/api/v1")
            .RequireAuthorization()
            .WithTags("Document Conversion")
            .WithOpenApi();

        // Convert DOCX to PDF endpoint
        group.MapPost("/documentconversion", ConvertFileToPdf)
            .WithName("ConvertFileToPdf")
            .WithSummary("Converts a file to PDF using Microsoft Graph API")
            .WithDescription("Converts a file to PDF format. The file must be less than 100MB.")
            .Accepts<Model.Document>("application/json")
            .Produces<Model.Document>(200, "application/json")
            .ProducesValidationProblem(400)
            .Produces(401)
            .Produces(500);

        // Health check endpoint
        group.MapGet("/health", GetHealth)
            .WithName("GetDocumentConversionHealth")
            .WithSummary("Gets the health status of the document conversion service")
            .WithDescription("Returns information about the document conversion service status and capabilities")
            .Produces(200)
            .Produces(401)
            .Produces(500);
    }

    private static async Task<IResult> ConvertFileToPdf(
        [FromBody][Required] Model.Document document,
        //IDocumentConversionService documentConversionService,
        IDocumentConverter documentConverter,
        ILogger<DocumentConversionEndpointsLogger> logger)
    {
        try
        {
            logger.LogInformation("Received request to convert document: {Name}", document?.Name);
            if (document == null || string.IsNullOrWhiteSpace(document.Content) || string.IsNullOrWhiteSpace(document.Name))
            {
                logger.LogWarning("No document provided or document is empty");
                return Results.BadRequest(new { error = "No document provided or document is empty" });
            }
            // if (!document.Name.EndsWith(".docx", StringComparison.OrdinalIgnoreCase))
            // {
            //     logger.LogWarning("Invalid file type: {Name}. Only DOCX files are supported", document.Name);
            //     return Results.BadRequest(new { error = "Only DOCX files are supported" });
            // }

            string content64 = string.Empty;
            bool isXml = document.Content.Contains("<file>");
            if (!string.IsNullOrEmpty(document.Content))
            {
                if (document.Content.Contains("<file>"))
                {
                    content64 = XmlFileParser.GetContentFromXml(document.Content);
                    isXml = true;
                }
                else
                    content64 = document.Content;
            }
            var fileBytes = Convert.FromBase64String(content64);

            // Check file size (limit to 10MB)
            const long maxFileSize = 50 * 1024 * 1024; // 50MB
            if (fileBytes.Length > maxFileSize)
            {
                logger.LogWarning("File {Name} is too large: {FileSize} bytes", document.Name, fileBytes.Length);
                return Results.BadRequest(new { error = "File size must be less than 50MB" });
            }

            using var stream = new MemoryStream(fileBytes);
            logger.LogInformation("----------------------------- Starting conversion process ------------------------------");
            logger.LogInformation("Converting {OriginalFileName} to PDF", document.Name);
            var conversionResult = await documentConverter.ConvertStreamAsync(stream, document.Name);

            //var pdfBytes = await documentConversionService.ConvertDocxToPdfAsync(stream, document.Name);
            var pdfBase64 = Convert.ToBase64String(conversionResult.PdfData);
            var pdfFileName = Path.GetFileNameWithoutExtension(document.Name) + ".pdf";
            logger.LogInformation("Successfully converted {OriginalFileName} to {PdfFileName}", document.Name, pdfFileName);
            string output = string.Empty;
            if (isXml)
                output = $"<file><name>{pdfFileName}</name><content>{pdfBase64}</content></file>";
            else
                output = pdfBase64;
            
            var result = new Model.Document { Name = pdfFileName, Content = output };
            return Results.Ok(result);
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning(ex, "Invalid argument provided for conversion");
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (FormatException ex)
        {
            logger.LogWarning(ex, "Invalid file format provided for conversion");
            return Results.BadRequest(new { error = ex.Message + " Possible a SmartObject disconfigured" });
        }
        
        catch (InvalidOperationException ex)
        {
            logger.LogError(ex, "Configuration or operation error during conversion");
            return Results.Problem($"Service configuration error: {ex.Message}", statusCode: 500);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during document conversion");
            return Results.Problem($"An unexpected error occurred during conversion: {ex.Message}", statusCode: 500);
        }
    }

    private static IResult GetHealth(ILogger<DocumentConversionEndpointsLogger> logger)
    {
        try
        {
            return Results.Ok(new
            {
                service = "DocumentConversionService",
                status = "healthy",
                timestamp = DateTime.UtcNow,
                supportedFormats = new[] { "all documents" },
                outputFormat = "pdf",
                maxFileSize = "10OMB"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error checking service health");
            return Results.Problem("Service health check failed", statusCode: 500);
        }
    }
}
