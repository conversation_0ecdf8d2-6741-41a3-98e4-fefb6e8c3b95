2025-08-12 11:43:08.912 +02:00 [INF] Starting web application
2025-08-12 11:43:09.555 +02:00 [FTL] Application terminated unexpectedly
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
 ---> System.InvalidOperationException: No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Program.cs:line 84
2025-08-12 11:46:22.220 +02:00 [INF] Starting web application
2025-08-12 11:46:22.523 +02:00 [FTL] Application terminated unexpectedly
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
 ---> System.InvalidOperationException: No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Program.cs:line 86
2025-08-12 11:46:39.685 +02:00 [INF] Starting web application
2025-08-12 11:46:39.979 +02:00 [FTL] Application terminated unexpectedly
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
 ---> System.InvalidOperationException: No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Program.cs:line 86
2025-08-12 11:47:57.769 +02:00 [INF] Starting web application
2025-08-12 11:47:58.076 +02:00 [FTL] Application terminated unexpectedly
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
 ---> System.InvalidOperationException: No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Program.cs:line 86
2025-08-12 11:49:24.190 +02:00 [INF] Starting web application
2025-08-12 11:49:24.432 +02:00 [FTL] Application terminated unexpectedly
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: ProcessPoint.GraphApiServices.Services.IDocumentConverter Lifetime: Scoped ImplementationType: ProcessPoint.GraphApiServices.Services.GraphDocumentConverter': No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
 ---> System.InvalidOperationException: No constructor for type 'ProcessPoint.GraphApiServices.Services.GraphDocumentConverter' can be instantiated using services from the service container and default values.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Program.cs:line 86
2025-08-12 11:49:58.281 +02:00 [INF] Starting web application
2025-08-12 11:49:58.536 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 11:49:58.586 +02:00 [INF] Creating key {2a5c8c2b-8810-4144-a6a0-e77dacf30716} with creation date 2025-08-12 09:49:58Z, activation date 2025-08-12 09:49:58Z, and expiration date 2025-11-10 09:49:58Z.
2025-08-12 11:49:58.589 +02:00 [WRN] No XML encryptor configured. Key {2a5c8c2b-8810-4144-a6a0-e77dacf30716} may be persisted to storage in unencrypted form.
2025-08-12 11:49:58.590 +02:00 [INF] Writing data to file '/Users/<USER>/.aspnet/DataProtection-Keys/key-2a5c8c2b-8810-4144-a6a0-e77dacf30716.xml'.
2025-08-12 11:49:58.680 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 11:49:58.681 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 11:49:58.681 +02:00 [INF] Hosting environment: Development
2025-08-12 11:49:58.681 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 11:50:58.199 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/ - null null
2025-08-12 11:50:58.218 +02:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 11:50:58.222 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:50:58.224 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:50:58.227 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/ - 404 0 null 28.6276ms
2025-08-12 11:50:58.230 +02:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5041/, Response status code: 404
2025-08-12 11:50:58.254 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/apple-touch-icon-precomposed.png - null null
2025-08-12 11:50:58.257 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:50:58.258 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:50:58.259 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/apple-touch-icon-precomposed.png - 404 0 null 5.0902ms
2025-08-12 11:50:58.259 +02:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5041/apple-touch-icon-precomposed.png, Response status code: 404
2025-08-12 11:50:58.260 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/apple-touch-icon.png - null null
2025-08-12 11:50:58.262 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:50:58.263 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:50:58.263 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/apple-touch-icon.png - 404 0 null 2.3407ms
2025-08-12 11:50:58.263 +02:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5041/apple-touch-icon.png, Response status code: 404
2025-08-12 11:51:00.802 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/ - null null
2025-08-12 11:51:00.803 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:51:00.803 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:51:00.803 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/ - 404 0 null 0.7133ms
2025-08-12 11:51:00.803 +02:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5041/, Response status code: 404
2025-08-12 11:52:32.278 +02:00 [INF] Application is shutting down...
-12 11:52:09.869 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 11:52:09.951 +02:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5041: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-08-12 11:52:09.963 +02:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5041: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Program.cs:line 125
2025-08-12 11:58:08.546 +02:00 [INF] Starting web application
2025-08-12 11:58:08.770 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 11:58:08.849 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 11:58:08.850 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 11:58:08.851 +02:00 [INF] Hosting environment: Development
2025-08-12 11:58:08.851 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 11:58:17.810 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/ - null null
2025-08-12 11:58:17.829 +02:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 11:58:17.839 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:58:17.841 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:58:17.844 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/ - 404 0 null 34.3552ms
2025-08-12 11:58:17.847 +02:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5041/, Response status code: 404
2025-08-12 11:58:20.374 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/ - null null
2025-08-12 11:58:20.378 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:58:20.378 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-08-12 11:58:20.378 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/ - 404 0 null 5.3677ms
2025-08-12 11:58:20.379 +02:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5041/, Response status code: 404
2025-08-12 11:58:28.061 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/swagger/index.html - null null
2025-08-12 11:58:28.095 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/swagger/index.html - 200 null text/html;charset=utf-8 34.2434ms
2025-08-12 11:58:28.103 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/swagger/swagger-ui.css - null null
2025-08-12 11:58:28.108 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/swagger/swagger-ui-bundle.js - null null
2025-08-12 11:58:28.108 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/swagger/swagger-ui-standalone-preset.js - null null
2025-08-12 11:58:28.110 +02:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-08-12 11:58:28.110 +02:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-08-12 11:58:28.111 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/swagger/swagger-ui.css - 200 143943 text/css 7.6649ms
2025-08-12 11:58:28.111 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/swagger/swagger-ui-standalone-preset.js - 200 339486 text/javascript 2.8078ms
2025-08-12 11:58:28.112 +02:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-08-12 11:58:28.112 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/swagger/swagger-ui-bundle.js - 200 1096145 text/javascript 4.3066ms
2025-08-12 11:58:28.199 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/swagger/v1/swagger.json - null null
2025-08-12 11:58:28.201 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 11:58:28.236 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 36.9032ms
2025-08-12 12:03:06.969 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 45
2025-08-12 12:03:06.973 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-08-12 12:03:06.973 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-08-12 12:03:06.976 +02:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-12 12:03:06.976 +02:00 [INF] AuthenticationScheme: BasicAuthentication was challenged.
2025-08-12 12:03:06.977 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 401 0 null 7.7319ms
2025-08-12 12:04:21.311 +02:00 [INF] Starting web application
2025-08-12 12:04:21.520 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 12:04:21.599 +02:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5041: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-08-12 12:04:21.609 +02:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5041: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Program.cs:line 135
2025-08-12 12:21:05.384 +02:00 [INF] Starting web application
2025-08-12 12:21:05.632 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 12:21:05.716 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 12:21:05.718 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 12:21:05.718 +02:00 [INF] Hosting environment: Development
2025-08-12 12:21:05.718 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 12:21:23.742 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/swagger/index.html - null null
2025-08-12 12:21:23.786 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/swagger/index.html - 200 null text/html;charset=utf-8 43.8329ms
2025-08-12 12:21:23.884 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5041/swagger/v1/swagger.json - null null
2025-08-12 12:21:23.887 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 12:21:23.924 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5041/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 40.713ms
2025-08-12 12:22:14.698 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 48
2025-08-12 12:22:14.699 +02:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 12:22:14.710 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 12:22:14.713 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:22:14.821 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 12:22:14.821 +02:00 [WRN] Invalid file format provided for conversion
System.FormatException: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.
   at System.Convert.FromBase64CharPtr(Char* inputPtr, Int32 inputLength)
   at System.Convert.FromBase64String(String s)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 72
2025-08-12 12:22:14.832 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 12:22:14.836 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 12:22:14.838 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:22:14.839 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 400 null application/json; charset=utf-8 140.7695ms
2025-08-12 12:23:32.365 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 12:23:32.369 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 12:23:32.370 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:23:32.372 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 12:23:32.373 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 12:23:32.373 +02:00 [INF] Converting test.docx to PDF
2025-08-12 12:23:33.331 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 12:23:33.332 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 12:23:33.332 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 12:23:33.332 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:23:33.333 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 400 null application/json; charset=utf-8 968.1899ms
2025-08-12 12:24:27.786 +02:00 [INF] Starting web application
2025-08-12 12:24:28.505 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 12:24:29.248 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 12:24:29.249 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 12:24:29.251 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 12:24:29.252 +02:00 [INF] Hosting environment: Development
2025-08-12 12:24:29.252 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 12:24:32.550 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger - null null
2025-08-12 12:24:32.570 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger - 301 0 null 21.0331ms
2025-08-12 12:24:32.585 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 12:24:32.640 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 55.3619ms
2025-08-12 12:24:32.792 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 12:24:32.798 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 12:24:32.864 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 93.566ms
2025-08-12 12:24:37.608 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 12:24:37.610 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 2.2897ms
2025-08-12 12:24:37.660 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 12:24:37.681 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 12:24:37.686 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:24:37.788 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 12:24:37.788 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 12:24:37.788 +02:00 [INF] Converting test.docx to PDF
2025-08-12 12:24:39.082 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 12:24:39.111 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 12:24:39.116 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 12:24:39.124 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:24:39.127 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 1466.548ms
2025-08-12 12:25:02.208 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 12:25:02.208 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 0.3636ms
2025-08-12 12:25:02.209 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 12:25:02.212 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 12:25:02.215 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:25:02.219 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 12:25:21.512 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 12:25:22.563 +02:00 [INF] Converting test.docx to PDF
2025-08-12 12:25:53.397 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 12:25:53.426 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 12:25:53.426 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 12:25:53.426 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:25:53.426 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 51216.6923ms
2025-08-12 12:25:59.427 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 12:25:59.427 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 0.408ms
2025-08-12 12:25:59.428 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 12:25:59.429 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 12:25:59.429 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:25:59.430 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 12:25:59.430 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 12:26:03.541 +02:00 [INF] Converting test.docx to PDF
2025-08-12 12:26:04.061 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 12:26:04.062 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 12:26:04.062 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 12:26:04.062 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:26:04.062 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 4634.3807ms
2025-08-12 12:26:28.841 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 12:26:28.845 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 3.8293ms
2025-08-12 12:26:28.848 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 12:26:28.848 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 12:26:28.848 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:26:28.849 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 12:26:28.849 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 12:26:28.849 +02:00 [INF] Converting test.docx to PDF
2025-08-12 12:26:51.735 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 12:26:51.736 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 12:26:51.736 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 12:26:51.736 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:26:51.736 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 22887.7448ms
2025-08-12 12:26:59.394 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 12:26:59.394 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 0.8933ms
2025-08-12 12:26:59.397 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 12:26:59.397 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 12:26:59.398 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:26:59.399 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 12:26:59.399 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 12:26:59.399 +02:00 [INF] Converting test.docx to PDF
2025-08-12 12:28:34.605 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 12:28:34.605 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 12:28:34.606 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 12:28:34.606 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 12:28:34.606 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 95208.6045ms
2025-08-12 13:37:20.066 +02:00 [INF] Starting web application
2025-08-12 13:37:20.306 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 13:37:20.860 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 13:37:20.860 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 13:37:20.862 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 13:37:20.862 +02:00 [INF] Hosting environment: Development
2025-08-12 13:37:20.862 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 13:37:21.522 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 13:37:21.596 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 74.2865ms
2025-08-12 13:37:21.702 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 13:37:21.706 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 13:37:21.755 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 52.7893ms
2025-08-12 13:37:25.874 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 13:37:25.876 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 1.6127ms
2025-08-12 13:37:25.910 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 13:37:25.928 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 13:37:25.932 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:37:25.988 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 13:37:25.988 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 13:37:25.989 +02:00 [INF] Converting test.docx to PDF
2025-08-12 13:37:27.099 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 13:37:27.113 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 13:37:27.117 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 13:37:27.123 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:37:27.125 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 1214.7489ms
2025-08-12 13:37:43.281 +02:00 [INF] Application is shutting down...
2025-08-12 13:37:46.346 +02:00 [INF] Starting web application
2025-08-12 13:37:46.868 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 13:37:47.463 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 13:37:47.464 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 13:37:47.466 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 13:37:47.466 +02:00 [INF] Hosting environment: Development
2025-08-12 13:37:47.466 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 13:37:47.936 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 13:37:48.027 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 90.7122ms
2025-08-12 13:37:48.131 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 13:37:48.135 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 13:37:48.199 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 68.3243ms
2025-08-12 13:37:52.801 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 13:37:52.803 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 2.0057ms
2025-08-12 13:37:52.912 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 13:37:52.939 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 13:37:52.944 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:37:53.038 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 13:37:53.038 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 13:37:53.038 +02:00 [INF] Converting test.docx to PDF
2025-08-12 13:40:10.305 +02:00 [INF] Starting web application
2025-08-12 13:40:10.992 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 13:40:11.642 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 13:40:11.643 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 13:40:11.645 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 13:40:11.646 +02:00 [INF] Hosting environment: Development
2025-08-12 13:40:11.646 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 13:40:12.228 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 13:40:12.318 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 89.8622ms
2025-08-12 13:40:12.420 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 13:40:12.425 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 13:40:12.489 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 68.5934ms
2025-08-12 13:40:20.666 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 13:40:20.669 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 2.5217ms
2025-08-12 13:40:20.724 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 13:40:20.749 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 13:40:20.754 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:40:20.851 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 13:40:20.851 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 13:40:20.851 +02:00 [INF] Converting test.docx to PDF
2025-08-12 13:40:44.250 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 13:40:44.282 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 13:40:44.287 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 13:40:44.295 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:40:44.298 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 23573.6864ms
2025-08-12 13:40:56.660 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 13:40:56.661 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 1.1555ms
2025-08-12 13:40:56.664 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 13:40:56.665 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 13:40:56.666 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:40:56.670 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 13:40:56.670 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 13:40:56.670 +02:00 [INF] Converting test.docx to PDF
2025-08-12 13:42:47.664 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 13:42:47.665 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 13:42:47.665 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 13:42:47.665 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:42:47.665 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 111001.1827ms
2025-08-12 13:43:31.043 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 13:43:31.048 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 2.6865ms
2025-08-12 13:43:31.049 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 13:43:31.050 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 13:43:31.050 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:43:31.051 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 13:43:31.051 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 13:43:31.051 +02:00 [INF] Converting test.docx to PDF
2025-08-12 13:51:51.930 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 13:51:51.933 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 13:51:51.933 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 13:51:51.934 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:51:51.934 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 500864.3014ms
2025-08-12 13:55:21.928 +02:00 [INF] Starting web application
2025-08-12 13:55:22.483 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 13:55:23.191 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 13:55:23.192 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 13:55:23.195 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 13:55:23.195 +02:00 [INF] Hosting environment: Development
2025-08-12 13:55:23.195 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 13:55:23.832 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 13:55:23.924 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 92.4004ms
2025-08-12 13:55:24.030 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 13:55:24.036 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 13:55:24.100 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 69.9007ms
2025-08-12 13:55:38.550 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 13:55:38.554 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 4.5738ms
2025-08-12 13:55:38.634 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 13:55:38.661 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 13:55:38.667 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:55:38.766 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 13:55:38.766 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 13:55:38.766 +02:00 [INF] Converting test.docx to PDF
2025-08-12 13:55:46.216 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 13:55:46.246 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 13:55:46.250 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 13:55:46.256 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:55:46.258 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 7623.6599ms
2025-08-12 13:55:56.293 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 13:55:56.293 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 0.357ms
2025-08-12 13:55:56.294 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 13:55:56.296 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 13:55:56.297 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:55:56.298 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 13:55:56.298 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 13:55:56.298 +02:00 [INF] Converting test.docx to PDF
2025-08-12 13:56:53.112 +02:00 [INF] Starting web application
2025-08-12 13:56:53.669 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 13:56:54.307 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 13:56:54.307 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 13:56:54.310 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 13:56:54.310 +02:00 [INF] Hosting environment: Development
2025-08-12 13:56:54.310 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 13:56:54.903 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 13:56:54.987 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 84.1024ms
2025-08-12 13:56:55.084 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 13:56:55.089 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 13:56:55.152 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 67.6272ms
2025-08-12 13:56:59.882 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 13:56:59.884 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 1.9068ms
2025-08-12 13:57:00.086 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 13:57:00.106 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 13:57:00.110 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:57:00.211 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 13:57:00.212 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 13:57:00.212 +02:00 [INF] Converting test.docx to PDF
2025-08-12 13:57:31.266 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 13:57:31.271 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 13:57:31.275 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 13:57:31.281 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:57:31.283 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 31197.649ms
2025-08-12 13:58:28.356 +02:00 [INF] Starting web application
2025-08-12 13:58:28.600 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 13:58:29.109 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 13:58:29.110 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 13:58:29.112 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 13:58:29.112 +02:00 [INF] Hosting environment: Development
2025-08-12 13:58:29.112 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 13:58:29.235 +02:00 [INF] Application is shutting down...
2025-08-12 13:58:32.843 +02:00 [INF] Starting web application
2025-08-12 13:58:33.326 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 13:58:33.920 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 13:58:33.921 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 13:58:33.923 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 13:58:33.924 +02:00 [INF] Hosting environment: Development
2025-08-12 13:58:33.924 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 13:58:34.523 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 13:58:34.609 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 86.467ms
2025-08-12 13:58:34.714 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 13:58:34.718 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 13:58:34.781 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 67.0634ms
2025-08-12 13:58:39.514 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 13:58:39.515 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 1.5962ms
2025-08-12 13:58:39.542 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 13:58:39.561 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 13:58:39.565 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 13:58:39.662 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 13:58:39.663 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 13:58:39.663 +02:00 [INF] Converting test.docx to PDF
2025-08-12 14:00:54.982 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 14:00:54.987 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 14:00:54.991 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 14:00:54.997 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 14:00:54.999 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 135455.8091ms
2025-08-12 14:37:34.846 +02:00 [INF] Starting web application
2025-08-12 14:37:35.555 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 14:37:36.318 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 14:37:36.319 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 14:37:36.322 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 14:37:36.322 +02:00 [INF] Hosting environment: Development
2025-08-12 14:37:36.322 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 14:37:36.724 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 14:37:36.817 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 93.1319ms
2025-08-12 14:37:36.931 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 14:37:36.935 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 14:37:37.001 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 70.1446ms
2025-08-12 14:37:42.222 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 14:37:42.224 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 1.875ms
2025-08-12 14:37:42.279 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 14:37:42.301 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 14:37:42.306 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 14:37:42.425 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 14:37:42.425 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 14:37:42.425 +02:00 [INF] Converting test.docx to PDF
2025-08-12 14:39:06.498 +02:00 [INF] Starting web application
2025-08-12 14:39:07.305 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 14:39:08.191 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 14:39:08.191 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 14:39:08.194 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 14:39:08.194 +02:00 [INF] Hosting environment: Development
2025-08-12 14:39:08.194 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 14:39:08.712 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 14:39:08.799 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 88.2267ms
2025-08-12 14:39:08.911 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 14:39:08.915 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 14:39:09.011 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 100.3117ms
2025-08-12 14:39:16.143 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 14:39:16.145 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 1.9325ms
2025-08-12 14:39:16.306 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 14:39:16.328 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 14:39:16.332 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 14:39:16.452 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 14:39:16.452 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 14:39:16.452 +02:00 [INF] Converting test.docx to PDF
2025-08-12 18:23:52.994 +02:00 [INF] Starting web application
2025-08-12 18:23:53.515 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 18:23:54.201 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 18:23:54.202 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 18:23:54.204 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 18:23:54.204 +02:00 [INF] Hosting environment: Development
2025-08-12 18:23:54.204 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 18:23:54.625 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 18:23:54.713 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 88.2745ms
2025-08-12 18:23:54.818 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 18:23:54.822 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 18:23:54.888 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 70.2881ms
2025-08-12 18:23:57.720 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - application/json 18726
2025-08-12 18:23:57.723 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5041/api/v1/documentconversion - 307 0 null 2.0251ms
2025-08-12 18:23:57.779 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 18:23:57.801 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 18:23:57.806 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 18:23:57.899 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 18:23:57.899 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 18:23:57.899 +02:00 [INF] Converting test.docx to PDF
2025-08-12 18:24:56.353 +02:00 [INF] Successfully converted test.docx to test.pdf
2025-08-12 18:24:56.357 +02:00 [INF] Setting HTTP status code 200.
2025-08-12 18:24:56.357 +02:00 [INF] Writing value of type 'Document' as Json.
2025-08-12 18:24:56.364 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 18:24:56.366 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 200 null application/json; charset=utf-8 58586.2098ms
2025-08-12 18:30:56.645 +02:00 [INF] Starting web application
2025-08-12 18:30:56.901 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 18:30:57.420 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 18:30:57.421 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 18:30:57.422 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 18:30:57.422 +02:00 [INF] Hosting environment: Development
2025-08-12 18:30:57.423 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 18:30:57.917 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 18:30:57.989 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 72.6553ms
2025-08-12 18:30:58.092 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 18:30:58.096 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 18:30:58.143 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 50.6737ms
2025-08-12 18:31:22.393 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 18:31:22.416 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 18:31:22.421 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 18:31:22.483 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 18:31:22.484 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 18:31:22.484 +02:00 [INF] Converting test.docx to PDF
2025-08-12 18:31:26.707 +02:00 [INF] Successfully converted test.docx to test.pdf
2025-08-12 18:31:26.713 +02:00 [INF] Setting HTTP status code 200.
2025-08-12 18:31:26.713 +02:00 [INF] Writing value of type 'Document' as Json.
2025-08-12 18:31:26.721 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 18:31:26.723 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 200 null application/json; charset=utf-8 4330.4471ms
2025-08-12 18:37:52.472 +02:00 [INF] Application is shutting down...
2025-08-12 19:03:44.708 +02:00 [INF] Starting web application
2025-08-12 19:03:44.972 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 19:03:45.582 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 19:03:45.582 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 19:03:45.584 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 19:03:45.584 +02:00 [INF] Hosting environment: Development
2025-08-12 19:03:45.584 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 19:03:46.077 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 19:03:46.149 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 71.8959ms
2025-08-12 19:03:46.260 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 19:03:46.263 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 19:03:46.312 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 52.5338ms
2025-08-12 19:03:56.049 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 19:03:56.070 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:03:56.074 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:03:56.133 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:03:56.133 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:03:56.133 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:03:56.134 +02:00 [INF] Start converting file test.docx
2025-08-12 19:03:56.135 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:03:57.978 +02:00 [INF] Waiting for file 01KKR2SLONMT6QMBDBM5BYTJIIAQCBXTHI to be processed
2025-08-12 19:03:58.498 +02:00 [INF] Converting file 01KKR2SLONMT6QMBDBM5BYTJIIAQCBXTHI to PDF
2025-08-12 19:03:59.947 +02:00 [INF] Successfully converted test.docx to test.pdf
2025-08-12 19:03:59.955 +02:00 [INF] Setting HTTP status code 200.
2025-08-12 19:03:59.955 +02:00 [INF] Writing value of type 'Document' as Json.
2025-08-12 19:03:59.966 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:03:59.969 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 200 null application/json; charset=utf-8 3919.0016ms
2025-08-12 19:04:43.117 +02:00 [INF] Application is shutting down...
2025-08-12 19:31:14.337 +02:00 [INF] Starting web application
2025-08-12 19:31:14.597 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 19:31:15.189 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 19:31:15.189 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 19:31:15.191 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 19:31:15.191 +02:00 [INF] Hosting environment: Development
2025-08-12 19:31:15.191 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 19:31:15.771 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 19:31:15.840 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 70.1192ms
2025-08-12 19:31:15.945 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 19:31:15.949 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 19:31:15.998 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 52.6641ms
2025-08-12 19:31:26.384 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 19:31:26.415 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:31:26.420 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:31:26.481 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:31:26.481 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:31:26.482 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:31:26.483 +02:00 [INF] Start processing file test.docx
2025-08-12 19:31:26.483 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:31:28.264 +02:00 [INF] Waiting for file 01KKR2SLP4ZAE5M3TP75CIFSJZOQG23KXU to be processed
2025-08-12 19:31:28.875 +02:00 [INF] File test_20250812173126.docx uploaded to OneDrive
2025-08-12 19:31:28.880 +02:00 [INF] Converting file 01KKR2SLP4ZAE5M3TP75CIFSJZOQG23KXU to PDF
2025-08-12 19:31:30.347 +02:00 [INF] Successfully converted test.docx to test.pdf
2025-08-12 19:31:30.354 +02:00 [INF] Setting HTTP status code 200.
2025-08-12 19:31:30.355 +02:00 [INF] Writing value of type 'Document' as Json.
2025-08-12 19:31:30.366 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:31:30.369 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 200 null application/json; charset=utf-8 3985.2762ms
2025-08-12 19:31:38.172 +02:00 [INF] Application is shutting down...
2025-08-12 19:45:24.439 +02:00 [INF] Starting web application
2025-08-12 19:45:24.684 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 19:45:25.268 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 19:45:25.268 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 19:45:25.270 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 19:45:25.270 +02:00 [INF] Hosting environment: Development
2025-08-12 19:45:25.270 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 19:45:25.875 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 19:45:25.949 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 73.8033ms
2025-08-12 19:45:26.056 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 19:45:26.060 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 19:45:26.108 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 51.6937ms
2025-08-12 19:45:33.817 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 18726
2025-08-12 19:45:33.846 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:45:33.850 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:45:33.913 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:45:33.914 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:45:33.914 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:45:33.915 +02:00 [INF] Start processing file test.docx
2025-08-12 19:45:33.916 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:45:35.941 +02:00 [INF] Waiting for file 01KKR2SLIF4WGZFOMKN5EYI7CHB77NKNXI to be processed
2025-08-12 19:45:36.321 +02:00 [INF] File test_20250812174533.docx uploaded to OneDrive
2025-08-12 19:45:36.325 +02:00 [INF] Converting file 01KKR2SLIF4WGZFOMKN5EYI7CHB77NKNXI to PDF
2025-08-12 19:45:38.277 +02:00 [INF] Successfully converted test.docx to test.pdf
2025-08-12 19:45:38.283 +02:00 [INF] Setting HTTP status code 200.
2025-08-12 19:45:38.284 +02:00 [INF] Writing value of type 'Document' as Json.
2025-08-12 19:45:38.294 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:45:38.297 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 200 null application/json; charset=utf-8 4480.4559ms
2025-08-12 19:47:12.358 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 14946
2025-08-12 19:47:12.360 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:47:12.361 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:47:12.362 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:47:12.362 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:47:12.362 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:47:12.362 +02:00 [INF] Start processing file test.docx
2025-08-12 19:47:12.362 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:47:13.651 +02:00 [INF] Waiting for file 01KKR2SLP7A4BVYDVISJEK7VKCZU3XCQFU to be processed
2025-08-12 19:47:14.035 +02:00 [INF] File test_20250812174712.docx uploaded to OneDrive
2025-08-12 19:47:14.035 +02:00 [INF] Converting file 01KKR2SLP7A4BVYDVISJEK7VKCZU3XCQFU to PDF
2025-08-12 19:47:15.812 +02:00 [INF] Successfully converted test.docx to test.pdf
2025-08-12 19:47:15.813 +02:00 [INF] Setting HTTP status code 200.
2025-08-12 19:47:15.814 +02:00 [INF] Writing value of type 'Document' as Json.
2025-08-12 19:47:15.827 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:47:15.827 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 200 null application/json; charset=utf-8 3469.526ms
2025-08-12 19:47:34.284 +02:00 [INF] Application is shutting down...
2025-08-12 19:51:46.432 +02:00 [INF] Starting web application
2025-08-12 19:51:46.682 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 19:51:47.191 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 19:51:47.191 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 19:51:47.193 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 19:51:47.193 +02:00 [INF] Hosting environment: Development
2025-08-12 19:51:47.193 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 19:51:47.636 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 19:51:47.705 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 69.2373ms
2025-08-12 19:51:47.808 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 19:51:47.812 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 19:51:47.859 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 51.077ms
2025-08-12 19:52:13.130 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 1369474
2025-08-12 19:52:13.151 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:52:13.155 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:52:13.219 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:52:13.222 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:52:13.222 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:52:13.223 +02:00 [INF] Start processing file test.docx
2025-08-12 19:52:13.224 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:52:16.406 +02:00 [INF] Waiting for file 01KKR2SLLOHDPA66US7ZCKVUHSJN2GENN3 to be processed
2025-08-12 19:52:16.872 +02:00 [INF] File test_20250812175213.docx uploaded to OneDrive
2025-08-12 19:52:16.876 +02:00 [INF] Converting file 01KKR2SLLOHDPA66US7ZCKVUHSJN2GENN3 to PDF
2025-08-12 19:52:18.080 +02:00 [ERR] Error converting file test.docx: The server returned an unexpected status code and no error factory is registered for this code: 415
Microsoft.Kiota.Abstractions.ApiException: The server returned an unexpected status code and no error factory is registered for this code: 415
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertToPdfAsync(String driveId, String itemId, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 255
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertStreamAsync(Stream fileStream, String fileName, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 84
2025-08-12 19:52:18.104 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 19:52:18.110 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 19:52:18.115 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 19:52:18.122 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:52:18.124 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 4993.7027ms
2025-08-12 19:52:31.530 +02:00 [INF] Application is shutting down...
2025-08-12 19:52:37.443 +02:00 [INF] Starting web application
2025-08-12 19:52:38.026 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 19:52:38.660 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 19:52:38.661 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 19:52:38.663 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 19:52:38.664 +02:00 [INF] Hosting environment: Development
2025-08-12 19:52:38.664 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 19:52:38.904 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 19:52:38.992 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 88.5118ms
2025-08-12 19:52:39.096 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 19:52:39.101 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 19:52:39.169 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 73.2637ms
2025-08-12 19:52:50.044 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 1369474
2025-08-12 19:52:50.068 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:52:50.073 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:52:50.173 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:52:50.174 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:52:50.174 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:52:50.175 +02:00 [INF] Start processing file test.docx
2025-08-12 19:52:50.176 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:52:53.087 +02:00 [INF] Waiting for file 01KKR2SLILSM6PVVT5QBFYQBHPEXSNREIW to be processed
2025-08-12 19:52:53.450 +02:00 [INF] File test_20250812175250.docx uploaded to OneDrive
2025-08-12 19:52:53.455 +02:00 [INF] Converting file 01KKR2SLILSM6PVVT5QBFYQBHPEXSNREIW to PDF
2025-08-12 19:52:55.096 +02:00 [ERR] Error converting file test.docx: The server returned an unexpected status code and no error factory is registered for this code: 415
Microsoft.Kiota.Abstractions.ApiException: The server returned an unexpected status code and no error factory is registered for this code: 415
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertToPdfAsync(String driveId, String itemId, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 255
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertStreamAsync(Stream fileStream, String fileName, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 84
2025-08-12 19:52:55.153 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 19:52:55.158 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 19:52:55.162 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 19:52:55.169 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:52:55.171 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 5127.6288ms
2025-08-12 19:53:42.344 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 1369474
2025-08-12 19:53:42.351 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:53:42.354 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:53:42.358 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:53:42.359 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:53:42.359 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:53:50.874 +02:00 [INF] Start processing file test.docx
2025-08-12 19:53:52.247 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:54:04.690 +02:00 [INF] Waiting for file 01KKR2SLKA2CXZCOWNGBE22OYHV7MJDF43 to be processed
2025-08-12 19:54:05.092 +02:00 [INF] File test_20250812175352.docx uploaded to OneDrive
2025-08-12 19:54:05.092 +02:00 [INF] Converting file 01KKR2SLKA2CXZCOWNGBE22OYHV7MJDF43 to PDF
2025-08-12 19:54:06.638 +02:00 [ERR] Error converting file test.docx: The server returned an unexpected status code and no error factory is registered for this code: 415
Microsoft.Kiota.Abstractions.ApiException: The server returned an unexpected status code and no error factory is registered for this code: 415
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertToPdfAsync(String driveId, String itemId, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 255
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertStreamAsync(Stream fileStream, String fileName, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 84
2025-08-12 19:54:06.663 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 19:54:06.664 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 19:54:06.664 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 19:54:06.664 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:54:06.664 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 24320.6865ms
2025-08-12 19:54:16.981 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 1369474
2025-08-12 19:54:16.985 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:54:16.986 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:54:16.989 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:54:16.991 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:54:16.991 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:54:19.442 +02:00 [INF] Start processing file test.docx
2025-08-12 19:54:20.616 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:54:29.617 +02:00 [INF] Waiting for file 01KKR2SLNYCTSQBGDOWJHIRPIME4J56Q2L to be processed
2025-08-12 19:54:54.079 +02:00 [INF] File test_20250812175420.docx uploaded to OneDrive
2025-08-12 19:55:08.765 +02:00 [INF] Converting file 01KKR2SLNYCTSQBGDOWJHIRPIME4J56Q2L to PDF
2025-08-12 19:55:40.572 +02:00 [ERR] Error converting file test.docx: The server returned an unexpected status code and no error factory is registered for this code: 415
Microsoft.Kiota.Abstractions.ApiException: The server returned an unexpected status code and no error factory is registered for this code: 415
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertToPdfAsync(String driveId, String itemId, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 255
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertStreamAsync(Stream fileStream, String fileName, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 84
2025-08-12 19:55:40.626 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 19:55:40.626 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 19:55:40.626 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 19:55:40.626 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:55:40.627 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 83645.321ms
2025-08-12 19:55:47.497 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 1369474
2025-08-12 19:55:47.498 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:55:47.498 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:55:47.507 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:55:47.509 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:55:47.509 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:55:51.247 +02:00 [INF] Start processing file test.docx
2025-08-12 19:55:54.332 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:56:05.884 +02:00 [INF] Waiting for file 01KKR2SLNPLTKQKENOBZBJZ4BDAAUK2CAZ to be processed
2025-08-12 19:56:06.288 +02:00 [INF] File test_20250812175554.docx uploaded to OneDrive
2025-08-12 19:56:06.288 +02:00 [INF] Converting file 01KKR2SLNPLTKQKENOBZBJZ4BDAAUK2CAZ to PDF
2025-08-12 19:58:34.915 +02:00 [INF] Starting web application
2025-08-12 19:58:35.523 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-12 19:58:36.200 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-12 19:58:36.200 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-12 19:58:36.203 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 19:58:36.203 +02:00 [INF] Hosting environment: Development
2025-08-12 19:58:36.203 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-12 19:58:36.831 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-12 19:58:36.916 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 84.6585ms
2025-08-12 19:58:37.021 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-12 19:58:37.025 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-12 19:58:37.085 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 64.1156ms
2025-08-12 19:58:40.575 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 1369474
2025-08-12 19:58:40.600 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 19:58:40.605 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 19:58:40.713 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 19:58:40.715 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 19:58:40.715 +02:00 [INF] Converting test.docx to PDF
2025-08-12 19:58:40.716 +02:00 [INF] Start processing file test.docx
2025-08-12 19:58:40.717 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 19:58:51.896 +02:00 [INF] Waiting for file 01KKR2SLJOGTFAKP6A6JFYNNPR2Y23FSU5 to be processed
2025-08-12 19:58:52.331 +02:00 [INF] File test_20250812175840.docx uploaded to OneDrive
2025-08-12 19:58:52.334 +02:00 [INF] Converting file 01KKR2SLJOGTFAKP6A6JFYNNPR2Y23FSU5 to PDF
2025-08-12 20:01:02.129 +02:00 [WRN] Request timed out. Retrying in "00:00:01" seconds
2025-08-12 20:01:14.915 +02:00 [ERR] Error converting file test.docx: The server returned an unexpected status code and no error factory is registered for this code: 415
Microsoft.Kiota.Abstractions.ApiException: The server returned an unexpected status code and no error factory is registered for this code: 415
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertToPdfAsync(String driveId, String itemId, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 250
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertStreamAsync(Stream fileStream, String fileName, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 84
2025-08-12 20:01:14.948 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 20:01:14.953 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 20:01:14.957 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 20:01:14.963 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 20:01:14.965 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 154389.7311ms
2025-08-12 20:01:18.924 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - application/json 1369474
2025-08-12 20:01:18.926 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-08-12 20:01:18.927 +02:00 [INF] Executing endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 20:01:18.933 +02:00 [INF] Received request to convert document: test.docx
2025-08-12 20:01:18.936 +02:00 [INF] ----------------------------- Starting conversion process ------------------------------
2025-08-12 20:01:18.936 +02:00 [INF] Converting test.docx to PDF
2025-08-12 20:01:18.936 +02:00 [INF] Start processing file test.docx
2025-08-12 20:01:18.936 +02:00 [INF] Uploading file test.docx to OneDrive
2025-08-12 20:01:21.647 +02:00 [INF] Waiting for file 01KKR2SLKLOBR6OEUFMRF37KO53YMZVO3S to be processed
2025-08-12 20:01:22.170 +02:00 [INF] File test_20250812180118.docx uploaded to OneDrive
2025-08-12 20:01:22.170 +02:00 [INF] Converting file 01KKR2SLKLOBR6OEUFMRF37KO53YMZVO3S to PDF
2025-08-12 20:02:57.440 +02:00 [WRN] Request timed out. Retrying in "00:00:01" seconds
2025-08-12 20:03:05.548 +02:00 [ERR] Error converting file test.docx: The server returned an unexpected status code and no error factory is registered for this code: 415
Microsoft.Kiota.Abstractions.ApiException: The server returned an unexpected status code and no error factory is registered for this code: 415
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendPrimitiveAsync[ModelType](RequestInformation requestInfo, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertToPdfAsync(String driveId, String itemId, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 250
   at ProcessPoint.GraphApiServices.Services.GraphDocumentConverter.ConvertStreamAsync(Stream fileStream, String fileName, ConversionOptions options) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Services/GraphDocumentConverter.cs:line 84
2025-08-12 20:03:05.573 +02:00 [WRN] Invalid argument provided for conversion
System.ArgumentNullException: Value cannot be null. (Parameter 'inArray')
   at System.ArgumentNullException.Throw(String paramName)
   at System.Convert.ToBase64String(Byte[] inArray)
   at ProcessPoint.GraphApiServices.Endpoints.DocumentConversionEndpoints.ConvertFileToPdf(Document document, IDocumentConverter documentConverter, ILogger`1 logger) in /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices/Endpoints/DocumentConversionEndpoints.cs:line 88
2025-08-12 20:03:05.573 +02:00 [INF] Setting HTTP status code 400.
2025-08-12 20:03:05.573 +02:00 [INF] Writing value of type '<>f__AnonymousType0`1' as Json.
2025-08-12 20:03:05.573 +02:00 [INF] Executed endpoint 'HTTP: POST /api/v1/documentconversion => ConvertFileToPdf'
2025-08-12 20:03:05.573 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:7235/api/v1/documentconversion - 400 null application/json; charset=utf-8 106649.3637ms
