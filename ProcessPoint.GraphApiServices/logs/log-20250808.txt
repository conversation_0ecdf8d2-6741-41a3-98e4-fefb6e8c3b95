2025-08-08 15:17:07.271 +02:00 [INF] Starting web application
2025-08-08 15:17:07.969 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-08-08 15:17:08.849 +02:00 [INF] Now listening on: https://localhost:7235
2025-08-08 15:17:08.850 +02:00 [INF] Now listening on: http://localhost:5041
2025-08-08 15:17:08.852 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-08 15:17:08.852 +02:00 [INF] Hosting environment: Development
2025-08-08 15:17:08.853 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.GraphApiServices/ProcessPoint.GraphApiServices
2025-08-08 15:17:11.163 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger - null null
2025-08-08 15:17:11.198 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger - 301 0 null 35.8028ms
2025-08-08 15:17:11.242 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/index.html - null null
2025-08-08 15:17:11.293 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/index.html - 200 null text/html;charset=utf-8 51.022ms
2025-08-08 15:17:11.453 +02:00 [INF] Request starting HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - null null
2025-08-08 15:17:11.457 +02:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-08-08 15:17:11.511 +02:00 [INF] Request finished HTTP/2 GET https://localhost:7235/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 58.5091ms
2025-08-08 15:17:31.010 +02:00 [INF] Application is shutting down...
