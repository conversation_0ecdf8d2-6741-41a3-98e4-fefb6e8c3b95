@ProcessPoint.GraphApiServices_HostAddress = http://localhost:5041

### Př<PERSON><PERSON><PERSON>šení pomocí jména a hesla
POST {{ProcessPoint.GraphApiServices_HostAddress}}/auth/login
Content-Type: application/json
Accept: application/json

{
  "username": "admin",
  "password": "admin123"
}

###

### Přihlášení legacy uživatele
POST {{ProcessPoint.GraphApiServices_HostAddress}}/auth/login
Content-Type: application/json
Accept: application/json

{
  "username": "legacy",
  "password": "legacy123"
}

###

### Generování testovacího tokenu (pouze v development módu)
POST {{ProcessPoint.GraphApiServices_HostAddress}}/auth/generate-token?userId=test123&userName=TestUser
Accept: application/json

###

### Testování ověření tokenu (nahraďte YOUR_TOKEN_HERE skutečným tokenem)
GET {{ProcessPoint.GraphApiServices_HostAddress}}/auth/validate
Accept: application/json
Authorization: Bearer YOUR_TOKEN_HERE

###

### Volání zabezpečeného endpointu (nahraďte YOUR_TOKEN_HERE skutečným tokenem)
GET {{ProcessPoint.GraphApiServices_HostAddress}}/weatherforecast/
Accept: application/json
Authorization: Bearer YOUR_TOKEN_HERE

###

### Pokus o volání bez tokenu (mělo by vrátit 401 Unauthorized)
GET {{ProcessPoint.GraphApiServices_HostAddress}}/weatherforecast/
Accept: application/json

###
