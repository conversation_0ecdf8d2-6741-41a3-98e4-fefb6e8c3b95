using System;
using System.Linq;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ProcessPoint.GraphApiServices.Services;

public class BasicAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    #region Property
    readonly IUserAuthenticationService _userService;
    #endregion

    #region Constructor
    public BasicAuthenticationHandler(IUserAuthenticationService userService,
        IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder)
        : base(options, logger, encoder)
    {
        _userService = userService;
    }
    #endregion

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        try
        {
            var authHeader = Request.Headers["Authorization"].ToString();
            if (string.IsNullOrEmpty(authHeader))
            {
                return Task.FromResult(AuthenticateResult.Fail("Authorization header not found."));
            }

            AuthenticationHeaderValue authHeaderValue;
            if (!AuthenticationHeaderValue.TryParse(authHeader, out authHeaderValue))
            {
                return Task.FromResult(AuthenticateResult.Fail("Invalid authorization header format."));
            }

            if (!string.Equals(authHeaderValue.Scheme, "Basic", StringComparison.OrdinalIgnoreCase))
            {
                return Task.FromResult(AuthenticateResult.Fail("Authorization scheme must be Basic."));
            }

            if (string.IsNullOrEmpty(authHeaderValue.Parameter))
            {
                return Task.FromResult(AuthenticateResult.Fail("Missing credentials."));
            }

            var credentialBytes = Convert.FromBase64String(authHeaderValue.Parameter);
            var credentials = Encoding.UTF8.GetString(credentialBytes).Split(':', 2);
            
            if (credentials.Length != 2)
            {
                return Task.FromResult(AuthenticateResult.Fail("Invalid credentials format."));
            }

            string username = credentials[0];
            string password = credentials[1];

            if (!_userService.ValidateCredentials(username, password))
            {
                return Task.FromResult(AuthenticateResult.Fail("Invalid username or password."));
            }

            var claims = new[] {
                new Claim(ClaimTypes.Name, username),
                new Claim(ClaimTypes.NameIdentifier, username)
            };

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return Task.FromResult(AuthenticateResult.Success(ticket));
        }
        catch (Exception ex)
        {
            Logger.LogWarning($"Authentication failed: {ex.Message}");
            return Task.FromResult(AuthenticateResult.Fail($"Authentication failed: {ex.Message}"));
        }
    }

}