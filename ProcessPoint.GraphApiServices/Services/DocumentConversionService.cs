using System;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Azure.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Kiota.Authentication.Azure;
using Microsoft.Graph.Models;

namespace ProcessPoint.GraphApiServices.Services;
public class DocumentConversionService : IDocumentConversionService
    {
        private readonly GraphServiceClient _graphServiceClient;
        private readonly ILogger<DocumentConversionService> _logger;
        private readonly IConfiguration _configuration;

        public DocumentConversionService(IConfiguration configuration, ILogger<DocumentConversionService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _graphServiceClient = CreateGraphServiceClient();
        }

        private GraphServiceClient CreateGraphServiceClient()
        {
            var clientId = _configuration["MicrosoftGraph:ClientId"];
            var clientSecret = _configuration["MicrosoftGraph:ClientSecret"];
            var tenantId = _configuration["MicrosoftGraph:TenantId"];

            if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret) || string.IsNullOrEmpty(tenantId))
            {
                throw new InvalidOperationException("Microsoft Graph configuration is missing. Please check appsettings.json.");
            }

            var credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
            var authProvider = new AzureIdentityAuthenticationProvider(credential);
            
            return new GraphServiceClient(authProvider);
        }

        public async Task<byte[]> ConvertDocxToPdfAsync(IFormFile docxFile)
        {
            if (docxFile == null || docxFile.Length == 0)
            {
                throw new ArgumentException("File is empty or null", nameof(docxFile));
            }

            // if (!docxFile.FileName.EndsWith(".docx", StringComparison.OrdinalIgnoreCase))
            // {
            //     throw new ArgumentException("File must be a DOCX document", nameof(docxFile));
            // }

            using var stream = docxFile.OpenReadStream();
            return await ConvertDocxToPdfAsync(stream, docxFile.FileName);
        }

        public async Task<byte[]> ConvertDocxToPdfAsync(Stream docxStream, string fileName)
        {
            string tempFileName = null;
            DriveItem uploadedItem = null;

            try
            {
                _logger.LogInformation("Starting conversion of {FileName} to PDF", fileName);

                // Generate unique filename to avoid conflicts
                tempFileName = $"temp_{Guid.NewGuid()}_{fileName}";

                // Upload the file to OneDrive
                uploadedItem = await UploadFileToOneDriveAsync(docxStream, tempFileName);

                if (uploadedItem?.Id == null)
                {
                    throw new InvalidOperationException("Failed to upload file to OneDrive");
                }

                // Wait a moment for the file to be processed
                await Task.Delay(2000);

                // Convert to PDF using Microsoft Graph
                var pdfContent = await ConvertToPdfAsync(uploadedItem.Id);

                _logger.LogInformation("Successfully converted {FileName} to PDF", fileName);
                return pdfContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting {FileName} to PDF", fileName);
                throw;
            }
            finally
            {
                // Clean up - delete the temporary file
                if (!string.IsNullOrEmpty(uploadedItem?.Id))
                {
                    try
                    {
                        await DeleteFileFromOneDriveAsync(uploadedItem.Id, tempFileName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to clean up temporary file {TempFileName}", tempFileName);
                    }
                }
            }
        }

        private async Task<DriveItem> UploadFileToOneDriveAsync(Stream fileStream, string fileName)
        {
            try
            {
                const int largeFileThreshold = 4 * 1024 * 1024; // 4 MB threshold
                if (fileStream.Length >= largeFileThreshold)
                {
                    return await UploadLargeFileAsync(fileStream, fileName);
                }

                // Pro menší soubory použijeme původní metodu
                var fileBytes = new byte[fileStream.Length];
                fileStream.Position = 0;
                await fileStream.ReadAsync(fileBytes, 0, fileBytes.Length);
                return await UploadFileDirectAsync(fileName, fileBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file {FileName} to OneDrive", fileName);
                throw;
            }
        }

        private async Task<DriveItem> UploadLargeFileAsync(Stream fileStream, string fileName)
        {
            try
            {
                using var httpClient = new HttpClient();
                var accessToken = await GetAccessTokenAsync();
                httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                // Získání SharePoint site ID
                var siteResponse = await httpClient.GetAsync("https://graph.microsoft.com/v1.0/sites/root");
                if (!siteResponse.IsSuccessStatusCode)
                {
                    throw new InvalidOperationException($"Failed to get SharePoint site: {siteResponse.StatusCode}");
                }

                var siteJson = await siteResponse.Content.ReadAsStringAsync();
                var siteData = JsonSerializer.Deserialize<JsonElement>(siteJson);
                var siteId = siteData.GetProperty("id").GetString();

                // Vytvoření upload session
                var createSessionUrl = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive/root:/{fileName}:/createUploadSession";
                var sessionResponse = await httpClient.PostAsync(createSessionUrl, null);
                if (!sessionResponse.IsSuccessStatusCode)
                {
                    throw new InvalidOperationException($"Failed to create upload session: {sessionResponse.StatusCode}");
                }

                var sessionJson = await sessionResponse.Content.ReadAsStringAsync();
                var sessionData = JsonSerializer.Deserialize<JsonElement>(sessionJson);
                var uploadUrl = sessionData.GetProperty("uploadUrl").GetString();

                // Nastavení velikosti fragmentu (musí být násobek 320 KB)
                const int fragmentSize = 320 * 1024 * 10; // 3.2 MB chunks
                var buffer = new byte[fragmentSize];
                long totalBytes = fileStream.Length;
                long bytesSent = 0;
                fileStream.Position = 0;

                while (bytesSent < totalBytes)
                {
                    int bytesRead = await fileStream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0) break;

                    var contentRange = $"bytes {bytesSent}-{bytesSent + bytesRead - 1}/{totalBytes}";
                    using var content = new ByteArrayContent(buffer, 0, bytesRead);
                    content.Headers.Add("Content-Range", contentRange);

                    var uploadResponse = await httpClient.PutAsync(uploadUrl, content);
                    if (!uploadResponse.IsSuccessStatusCode && uploadResponse.StatusCode != System.Net.HttpStatusCode.Accepted)
                    {
                        throw new InvalidOperationException($"Failed to upload file fragment: {uploadResponse.StatusCode}");
                    }

                    bytesSent += bytesRead;
                    _logger.LogInformation("Uploaded {BytesSent} of {TotalBytes} bytes for {FileName}", 
                        bytesSent, totalBytes, fileName);

                    // Pokud je to poslední fragment, získáme informace o souboru
                    if (bytesSent >= totalBytes)
                    {
                        var finalResponseJson = await uploadResponse.Content.ReadAsStringAsync();
                        var finalData = JsonSerializer.Deserialize<JsonElement>(finalResponseJson);

                        return new DriveItem
                        {
                            Id = finalData.GetProperty("id").GetString(),
                            Name = finalData.GetProperty("name").GetString(),
                            Size = finalData.GetProperty("size").GetInt64()
                        };
                    }
                }

                throw new InvalidOperationException("File upload was not completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading large file {FileName}", fileName);
                throw;
            }
        }

        private async Task<DriveItem> UploadFileDirectAsync(string fileName, byte[] fileBytes)
        {
            try
            {
                // Use HTTP client to upload file directly to Microsoft Graph
                using var httpClient = new HttpClient();

                // Get access token
                var accessToken = await GetAccessTokenAsync();

                httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                // Use SharePoint site instead of personal OneDrive for application permissions
                // First, get the default SharePoint site
                var siteResponse = await httpClient.GetAsync("https://graph.microsoft.com/v1.0/sites/root");
                if (!siteResponse.IsSuccessStatusCode)
                {
                    _logger.LogError("Failed to get SharePoint site: {StatusCode}", siteResponse.StatusCode);
                    throw new InvalidOperationException($"Failed to get SharePoint site: {siteResponse.StatusCode}");
                }

                var siteJson = await siteResponse.Content.ReadAsStringAsync();
                var siteData = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(siteJson);
                var siteId = siteData.GetProperty("id").GetString();

                // Upload file to SharePoint Documents library
                var uploadUrl = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive/root:/{fileName}:/content";
                var content = new ByteArrayContent(fileBytes);
                content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.wordprocessingml.document");

                var response = await httpClient.PutAsync(uploadUrl, content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new InvalidOperationException($"Failed to upload file: {response.StatusCode} - {errorContent}");
                }

                var responseJson = await response.Content.ReadAsStringAsync();
                _logger.LogDebug("Upload response JSON: {ResponseJson}", responseJson);

                // Parse the JSON response manually to extract the ID
                var jsonDocument = System.Text.Json.JsonDocument.Parse(responseJson);
                var root = jsonDocument.RootElement;

                // Extract required properties with null checks
                var id = root.TryGetProperty("id", out var idElement) ? idElement.GetString() : null;
                var name = root.TryGetProperty("name", out var nameElement) ? nameElement.GetString() : fileName;
                var size = root.TryGetProperty("size", out var sizeElement) ? sizeElement.GetInt64() : 0;

                if (string.IsNullOrEmpty(id))
                {
                    throw new InvalidOperationException("Failed to get file ID from upload response");
                }

                var driveItem = new DriveItem
                {
                    Id = id,
                    Name = name,
                    Size = size
                };

                // Try to parse dates if available
                if (root.TryGetProperty("createdDateTime", out var createdElement))
                {
                    if (DateTimeOffset.TryParse(createdElement.GetString(), out var createdDate))
                    {
                        driveItem.CreatedDateTime = createdDate;
                    }
                }

                if (root.TryGetProperty("lastModifiedDateTime", out var modifiedElement))
                {
                    if (DateTimeOffset.TryParse(modifiedElement.GetString(), out var modifiedDate))
                    {
                        driveItem.LastModifiedDateTime = modifiedDate;
                    }
                }

                _logger.LogInformation("Successfully uploaded file {FileName} with ID {FileId}", fileName, driveItem.Id);
                return driveItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in direct file upload for {FileName}", fileName);
                throw;
            }
        }


        private async Task<string> GetAccessTokenAsync()
        {
            try
            {
                var clientId = _configuration["MicrosoftGraph:ClientId"];
                var clientSecret = _configuration["MicrosoftGraph:ClientSecret"];
                var tenantId = _configuration["MicrosoftGraph:TenantId"];

                var credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
                var tokenRequestContext = new Azure.Core.TokenRequestContext(new[] { "https://graph.microsoft.com/.default" });
                var token = await credential.GetTokenAsync(tokenRequestContext);

                return token.Token;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get access token");
                throw;
            }
        }
        

        private async Task<byte[]> ConvertToPdfAsync(string driveItemId)
        {
            try
            {
                // Use direct HTTP call to convert the document to PDF
                using var httpClient = new HttpClient();

                // Get access token
                var accessToken = await GetAccessTokenAsync();

                httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                // Get the site ID first
                var siteResponse = await httpClient.GetAsync("https://graph.microsoft.com/v1.0/sites/root");
                if (!siteResponse.IsSuccessStatusCode)
                {
                    throw new InvalidOperationException($"Failed to get SharePoint site: {siteResponse.StatusCode}");
                }

                var siteJson = await siteResponse.Content.ReadAsStringAsync();
                var siteData = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(siteJson);
                var siteId = siteData.GetProperty("id").GetString();

                // Request PDF conversion from SharePoint
                var convertUrl = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive/items/{driveItemId}/content?format=pdf";
                var response = await httpClient.GetAsync(convertUrl);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Failed to convert document to PDF: {StatusCode} - {ErrorContent}", response.StatusCode, errorContent);
                    throw new InvalidOperationException($"Failed to convert document to PDF: {response.StatusCode} - {errorContent}");
                }

                return await response.Content.ReadAsByteArrayAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting drive item {DriveItemId} to PDF", driveItemId);
                throw;
            }
        }

        private async Task DeleteFileFromOneDriveAsync(string fileId, string fileName)
        {
            try
            {
                // Use direct HTTP call to delete the file
                using var httpClient = new HttpClient();

                // Get access token
                var accessToken = await GetAccessTokenAsync();

                httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                // Get the site ID first
                var siteResponse = await httpClient.GetAsync("https://graph.microsoft.com/v1.0/sites/root");
                if (!siteResponse.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Failed to get SharePoint site for cleanup: {StatusCode}", siteResponse.StatusCode);
                    return;
                }

                var siteJson = await siteResponse.Content.ReadAsStringAsync();
                var siteData = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(siteJson);
                var siteId = siteData.GetProperty("id").GetString();

                // Delete the file from SharePoint using file ID (more reliable than path)
                var deleteUrl = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive/items/{fileId}";
                var response = await httpClient.DeleteAsync(deleteUrl);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Deleted temporary file {FileName}", fileName);
                }
                else
                {
                    _logger.LogWarning("Failed to delete temporary file {FileName}: {StatusCode}", fileName, response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete temporary file {FileName}", fileName);
                // Don't throw here as this is cleanup - the main operation succeeded
            }
        }
    }

