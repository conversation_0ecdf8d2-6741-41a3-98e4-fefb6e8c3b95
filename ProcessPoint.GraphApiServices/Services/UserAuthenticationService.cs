using Microsoft.Extensions.Configuration;

namespace ProcessPoint.GraphApiServices.Services;

/// <summary>
/// Implementace služby pro ov<PERSON><PERSON><PERSON><PERSON> už<PERSON>k<PERSON>ch credentials
/// </summary>
public class UserAuthenticationService : IUserAuthenticationService
{
    private IConfiguration _configuration { get; }
    public UserAuthenticationService(IConfiguration configuration)
    {
        _configuration = configuration;
    }
    public bool ValidateCredentials(string username, string password)
    {
        return username.Equals(_configuration.GetSection("Credentials").GetSection("User").Value) && password.Equals(_configuration.GetSection("Credentials").GetSection("Password").Value);
    }
}