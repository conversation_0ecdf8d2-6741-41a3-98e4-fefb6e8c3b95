namespace ProcessPoint.GraphApiServices.Services;

public class ConversionResult
{
    public bool Success { get; set; }
    public byte[]? PdfData { get; set; }
    public string? OriginalFileName { get; set; }
    public string? PdfFileName { get; set; }
    public DateTime ConversionTime { get; set; }
    public string? SourceItemId { get; set; }
    public long SizeBytes { get; set; }
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Uložení PDF do souboru
    /// </summary>
    public async Task SaveToFileAsync(string outputPath = null)
    {
        if (!Success || PdfData == null)
            throw new InvalidOperationException("Konverze nebyla úspěšná");

        outputPath ??= PdfFileName;
        await File.WriteAllBytesAsync(outputPath, PdfData);
    }

    /// <summary>
    /// Získání PDF jako stream
    /// </summary>
    public Stream GetStream()
    {
        if (!Success || PdfData == null)
            throw new InvalidOperationException("Konverze nebyla ú<PERSON>ěšná");

        return new MemoryStream(PdfData);
    }
}

public class UploadResult
{
    public required string ItemId { get; set; }
    public required string DriveId { get; set; }
    public required string WebUrl { get; set; }
    public DateTimeOffset UploadedAt { get; set; }
}

public class ConversionOptions
{
    public required string TargetFolderPath { get; set; }
    public bool DeleteSourceAfterConversion { get; set; } = true;
    public int MaxRetries { get; set; } = 3;
    public int TimeoutSeconds { get; set; } = 60;
}

public class ConversionSettings
{
    public long MaxFileSizeBytes { get; set; } = 100 * 1024 * 1024; // 100 MB
    public int MaxConcurrentConversions { get; set; } = 5;
}