using Azure.Identity;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace ProcessPoint.GraphApiServices.Services;

public class GraphDocumentConverter : IDocumentConverter, IDisposable
{
    private readonly GraphServiceClient _graphClient;
    private readonly HttpClient _httpClient;
    private readonly ConversionSettings _settings;
    private readonly SemaphoreSlim _semaphore;
    private readonly ILogger<GraphDocumentConverter> _logger;

    // Konstruktor s nullable parametry
    public GraphDocumentConverter(GraphServiceClient graphClient, ConversionSettings? settings, ILogger<GraphDocumentConverter>? logger)
    {
        _graphClient = graphClient ?? throw new ArgumentNullException(nameof(graphClient));
        _settings = settings ?? new ConversionSettings();
        _httpClient = new HttpClient();
        _semaphore = new SemaphoreSlim(_settings.MaxConcurrentConversions);
        _logger = logger;
    }

    // <PERSON>uh<PERSON> konstruktor
    // public GraphDocumentConverter(string tenantId, string clientId, string clientSecret, ConversionSettings? settings = null)
    //     : this(CreateGraphClient(tenantId, clientId, clientSecret), settings)
    // {
    // }

    private static GraphServiceClient CreateGraphClient(string tenantId, string clientId, string clientSecret)
    {
        var clientSecretCredential = new ClientSecretCredential(
            tenantId, clientId, clientSecret,
            new ClientSecretCredentialOptions
            {
                AuthorityHost = AzureAuthorityHosts.AzurePublicCloud
            });

        return new GraphServiceClient(clientSecretCredential);
    }

    /// <summary>
    /// Hlavní metoda pro konverzi souboru do PDF
    /// </summary>
    public async Task<ConversionResult> ConvertFileAsync(string localFilePath, ConversionOptions? options = null)
    {
        if (string.IsNullOrEmpty(localFilePath))
            throw new ArgumentNullException(nameof(localFilePath));
            
        if (!File.Exists(localFilePath))
            throw new FileNotFoundException($"Soubor nenalezen: {localFilePath}");

        var fileInfo = new FileInfo(localFilePath);
        
        // Validace velikosti souboru
        if (fileInfo.Length > _settings.MaxFileSizeBytes)
            throw new InvalidOperationException($"Soubor překračuje maximální velikost {_settings.MaxFileSizeBytes / (1024 * 1024)} MB");

        // Validace formátu
        if (!IsSupported(fileInfo.Extension))
            throw new NotSupportedException($"Formát {fileInfo.Extension} není podporován pro konverzi");

        using var fileStream = File.OpenRead(localFilePath);
        return await ConvertStreamAsync(fileStream, fileInfo.Name, options);
    }

    /// <summary>
    /// Konverze ze streamu
    /// </summary>
    public async Task<ConversionResult> ConvertStreamAsync(Stream fileStream, string fileName, ConversionOptions? options = null)
    {
        options ??= new ConversionOptions { TargetFolderPath = "temp" };
        
        _logger.LogInformation("Start processing file {FileName}", fileName);
        
        await _semaphore.WaitAsync();
        try
        {
            // 1. Upload souboru
            var uploadResult = await UploadFileDirectAsync(fileStream, fileName, options);
            
            // 2. Konverze do PDF
            var pdfData = await ConvertToPdfAsync(uploadResult.DriveId, uploadResult.ItemId, options);
            
            // 3. Cleanup (pokud je požadováno)
            if (options.DeleteSourceAfterConversion)
            {
                await DeleteUploadedFileAsync(uploadResult.DriveId, uploadResult.ItemId);
            }
            
            return new ConversionResult
            {
                Success = true,
                PdfData = pdfData,
                OriginalFileName = fileName,
                PdfFileName = Path.GetFileNameWithoutExtension(fileName) + ".pdf",
                ConversionTime = DateTime.UtcNow,
                SourceItemId = uploadResult.ItemId,
                SizeBytes = pdfData.Length
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting file {FileName}: {ErrorMessage}", fileName, ex.Message);
            return new ConversionResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                OriginalFileName = fileName,
                ConversionTime = DateTime.UtcNow
            };
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// Optimalizovaný upload souboru
    /// </summary>
    private async Task<UploadResult> UploadFileDirectAsync(Stream fileStream, string fileName, ConversionOptions options)
    {
        _logger.LogInformation("Uploading file {FileName} to OneDrive", fileName);
        try
        {
            fileName = SanitizeFileName(fileName);
            var uploadPath = GetUploadPath(fileName, options);
            
            DriveItem uploadedItem;
            
            // Pro malé soubory (< 4MB) použít jednoduchý upload
            if (fileStream.Length < 4 * 1024 * 1024)
            {
                uploadedItem = await SimpleUploadAsync(fileStream, uploadPath);
            }
            else
            {
                // Pro velké soubory použít session upload
                uploadedItem = await LargeFileUploadAsync(fileStream, uploadPath);
            }
            
            // Počkat na zpracování souboru
            await WaitForFileProcessingAsync(uploadedItem.Id);
            _logger.LogInformation("File {FileName} uploaded to OneDrive", fileName);
            return new UploadResult
            {
                ItemId = uploadedItem.Id,
                DriveId = uploadedItem.ParentReference?.DriveId,
                WebUrl = uploadedItem.WebUrl,
                UploadedAt = DateTimeOffset.UtcNow
            };
        }
        catch (ServiceException ex)
        {
            _logger.LogError(ex, "Error uploading file {FileName} to OneDrive: {ErrorMessage", fileName, ex.Message);
            throw new ApplicationException($"Chyba při nahrávání souboru: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Získání SharePoint drive
    /// </summary>
    private async Task<(string DriveId, string RootId)> GetSharePointDriveAsync()
    {
        var drive = await _graphClient
            .Sites["root"]
            .Drive
            .GetAsync();

        var root = await _graphClient
            .Drives[drive?.Id]
            .Root
            .GetAsync();

        return (drive?.Id ?? throw new InvalidOperationException("Nepodařilo se získat SharePoint drive"),
                root?.Id ?? throw new InvalidOperationException("Nepodařilo se získat root SharePoint drive"));
    }

    /// <summary>
    /// Jednoduchý upload pro malé soubory
    /// </summary>
    private async Task<DriveItem> SimpleUploadAsync(Stream fileStream, string uploadPath)
    {
        await using (fileStream)
        {
            var (driveId, rootId) = await GetSharePointDriveAsync();

            var item = await _graphClient
                .Drives[driveId]
                .Items[rootId]
                .ItemWithPath(uploadPath)
                .Content
                .PutAsync(fileStream);

            return item ?? throw new InvalidOperationException("Nepodarilo se nahrát soubor");
        }
    }

    /// <summary>
    /// Upload velkých souborů pomocí upload session
    /// </summary>
    private async Task<DriveItem> LargeFileUploadAsync(Stream fileStream, string uploadPath)
    {
        _logger.LogInformation("Uploading large file {FileName} to SharePoint using upload session", uploadPath);
        var uploadSessionRequestBody = new Microsoft.Graph.Drives.Item.Items.Item.CreateUploadSession.CreateUploadSessionPostRequestBody
        {
            Item = new DriveItemUploadableProperties
            {
                AdditionalData = new Dictionary<string, object>
                {
                    { "@microsoft.graph.conflictBehavior", "rename" }
                }
            }
        };

        var (driveId, rootId) = await GetSharePointDriveAsync();

        var uploadSession = await _graphClient.Drives[driveId].Items[rootId]
            .ItemWithPath(uploadPath)
            .CreateUploadSession
            .PostAsync(uploadSessionRequestBody);

        // Upload po částech (chunks)
        var maxChunkSize = 320 * 1024 * 10; // 3.2 MB chunks
        var provider = new LargeFileUploadTask<DriveItem>(uploadSession, fileStream, maxChunkSize);

        var uploadResult = await provider.UploadAsync();

        if (!uploadResult.UploadSucceeded)
        {
            throw new ApplicationException("Upload velkého souboru selhal");
        }

        return uploadResult.ItemResponse;
    }

    /// <summary>
    /// Optimalizovaná konverze do PDF s retry logikou
    /// </summary>
    private async Task<byte[]> ConvertToPdfAsync(string driveId, string itemId, ConversionOptions options)
    {
        _logger.LogInformation("Converting file {ItemId} to PDF", itemId);
        var maxRetries = options.MaxRetries;
        var retryDelay = TimeSpan.FromSeconds(1);
        
        for (int attempt = 0; attempt <= maxRetries; attempt++)
        {
            try
            {
                // Použití SDK s query parametrem pro PDF
                var requestInfo = _graphClient.Drives[driveId]
                    .Items[itemId]
                    .Content
                    .ToGetRequestInformation(config =>
                    {
                        config.QueryParameters.Format = "pdf";
                    });
                
                // Přímé volání s timeoutem
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(options.TimeoutSeconds));
                var response = await _graphClient.RequestAdapter.SendPrimitiveAsync<Stream>(requestInfo, cancellationToken: cts.Token);
                
                if (response == null)
                    throw new ApplicationException("Graph API vrátilo prázdnou odpověď");
                
                // Konverze na byte array s optimalizací paměti
                using var memoryStream = new MemoryStream();
                await response.CopyToAsync(memoryStream, 81920, cts.Token); // 80KB buffer
                return memoryStream.ToArray();
            }
            catch (ServiceException ex) when (ex.ResponseStatusCode == 429 && attempt < maxRetries)
            {
                _logger.LogWarning(ex, "Rate limit exceeded. Retrying in {RetryAfter} seconds", ex.ResponseHeaders?.RetryAfter);
                // Rate limiting - počkat a zkusit znovu
                var retryAfter = GetRetryAfterValue(ex);
                await Task.Delay(retryAfter ?? retryDelay);
                retryDelay = TimeSpan.FromSeconds(retryDelay.TotalSeconds * 2); // Exponential backoff
            }
            catch (ServiceException ex) when ((int)ex.ResponseStatusCode >= 500 && attempt < maxRetries)
            {
                _logger.LogWarning(ex, "Server error. Retrying in {RetryAfter} seconds", ex.ResponseHeaders?.RetryAfter);
                // Server error - zkusit znovu
                await Task.Delay(retryDelay);
                retryDelay = TimeSpan.FromSeconds(Math.Min(retryDelay.TotalSeconds * 2, 30));
            }
            catch (TaskCanceledException) when (attempt < maxRetries)
            {
                _logger.LogWarning("Request timed out. Retrying in {RetryAfter} seconds", retryDelay);
                // Timeout - zkusit znovu s delším timeoutem
                options.TimeoutSeconds = Math.Min(options.TimeoutSeconds * 2, 300);
                await Task.Delay(retryDelay);
            }
        }
        _logger.LogWarning("Failed to convert file {ItemId} to PDF after {MaxRetries} attempts", itemId, maxRetries);
        throw new ApplicationException($"Konverze do PDF selhala po {maxRetries} pokusech");
    }

    /// <summary>
    /// Čekání na zpracování souboru v SharePointu
    /// </summary>
    private async Task WaitForFileProcessingAsync(string itemId, int maxWaitSeconds = 30)
    {
        var startTime = DateTime.UtcNow;
        _logger.LogInformation("Waiting for file {ItemId} to be processed", itemId);
        while ((DateTime.UtcNow - startTime).TotalSeconds < maxWaitSeconds)
        {
            try
            {
                var (driveId, _) = await GetSharePointDriveAsync();

                var item = await _graphClient
                    .Drives[driveId]
                    .Items[itemId]
                    .GetAsync();

                // Zkontrolovat, zda je soubor připraven
                if (item?.File != null && item.Size > 0)
                {
                    return;
                }
            }
            catch (ServiceException)
            {
                _logger.LogWarning("Error during waiting for file {ItemId} to be processed", itemId);
                // Ignorovat chyby během čekání
            }

            await Task.Delay(1000);
        }
    }

    /// <summary>
    /// Pomocná metoda pro smazání nahraného souboru
    /// </summary>
    private async Task DeleteUploadedFileAsync(string driveId, string itemId)
    {
        try
        {
            await _graphClient.Drives[driveId].Items[itemId].DeleteAsync();
        }
        catch (ServiceException ex)
        {
            // Log, ale neházet výjimku - konverze již proběhla
            Console.WriteLine($"Varování: Nepodařilo se smazat zdrojový soubor: {ex.Message}");
        }
    }

    /// <summary>
    /// Získání hodnoty Retry-After z hlaviček
    /// </summary>
    private TimeSpan? GetRetryAfterValue(ServiceException ex)
    {
        if (ex.ResponseHeaders?.RetryAfter != null)
        {
            if (ex.ResponseHeaders.RetryAfter.Delta.HasValue)
                return ex.ResponseHeaders.RetryAfter.Delta.Value;
            
            if (ex.ResponseHeaders.RetryAfter.Date.HasValue)
                return ex.ResponseHeaders.RetryAfter.Date.Value - DateTimeOffset.UtcNow;
        }
        return null;
    }

    /// <summary>
    /// Kontrola podporovaných formátů
    /// </summary>
    private bool IsSupported(string extension)
    {
        var supportedExtensions = new[]
        {
            ".doc", ".docx", ".docm", ".dot", ".dotx", ".dotm",  // Word
            ".xls", ".xlsx", ".xlsm", ".xlsb", ".xltx", ".xltm",  // Excel
            ".ppt", ".pptx", ".pptm", ".pps", ".ppsx", ".ppsm",   // PowerPoint
            ".rtf", ".odt", ".ods", ".odp", ".eml", ".msg",       // Ostatní
        };
        
        return supportedExtensions.Contains(extension.ToLowerInvariant());
    }

    /// <summary>
    /// Sanitizace názvu souboru
    /// </summary>
    private string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        
        // Zajistit unikátnost přidáním timestamp
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(sanitized);
        var extension = Path.GetExtension(sanitized);
        
        return $"{nameWithoutExtension}_{DateTime.UtcNow:yyyyMMddHHmmss}{extension}";
    }

    /// <summary>
    /// Získání cesty pro upload
    /// </summary>
    private string GetUploadPath(string fileName, ConversionOptions options)
    {
        if (!string.IsNullOrEmpty(options.TargetFolderPath))
        {
            return $"{options.TargetFolderPath.TrimEnd('/')}/{fileName}";
        }
        
        // Default: upload do temp složky
        return $"TempConversions/{DateTime.UtcNow:yyyy-MM-dd}/{fileName}";
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
        _semaphore?.Dispose();
    }
}