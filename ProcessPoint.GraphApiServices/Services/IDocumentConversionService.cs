using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace ProcessPoint.GraphApiServices.Services;

public interface IDocumentConversionService
{
    /// <summary>
    /// Converts a DOCX file to PDF using Microsoft Graph API
    /// </summary>
    /// <param name="docxFile">The DOCX file to convert</param>
    /// <returns>PDF file as byte array</returns>
    Task<byte[]> ConvertDocxToPdfAsync(IFormFile docxFile);

    /// <summary>
    /// Converts a DOCX file stream to PDF using Microsoft Graph API
    /// </summary>
    /// <param name="docxStream">The DOCX file stream to convert</param>
    /// <param name="fileName">Original file name</param>
    /// <returns>PDF file as byte array</returns>
    Task<byte[]> ConvertDocxToPdfAsync(Stream docxStream, string fileName);
}