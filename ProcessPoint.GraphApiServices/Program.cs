using System.Reflection;
using Microsoft.AspNetCore.Authentication;
using Microsoft.OpenApi.Models;
using ProcessPoint.GraphApiServices.Filters;
using ProcessPoint.GraphApiServices.Services;
using ProcessPoint.GraphApiServices.Endpoints;
using Serilog;
using Serilog.Events;
using Azure.Identity;
using Microsoft.Graph;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File(
        path: Path.Combine("logs", "log-.txt"),
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30,
        rollOnFileSizeLimit: true,
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}",
        fileSizeLimitBytes: 10_000_000) // 10MB per file
    .CreateLogger();

try
{
    Log.Information("Starting web application");
    
    var builder = WebApplication.CreateBuilder(args);
    
    // Add Serilog
    builder.Host.UseSerilog();

    // Add services to the container.
    // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
    builder.Services.AddEndpointsApiExplorer();

    // Konfigurace Swagger pro Bearer token
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo { Title = "ProcessPoint GraphAPI Services", Version = "v1" });

        // Přidání filtru pro odstranění additionalProperties: false
        c.SchemaFilter<RemoveAdditionalPropertiesSchemaFilter>();

        // Přidání Basic autentizace do Swagger
        c.AddSecurityDefinition("basic", new OpenApiSecurityScheme
        {
            Name = "Authorization",
            Type = SecuritySchemeType.Http,
            Scheme = "basic",
            In = ParameterLocation.Header,
            Description = "Authorization header using the Basic scheme."
        });

        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "basic"
                    }
                },
                Array.Empty<string>()
            }
        });
    });

    // Registrace služeb pro autentizaci
    builder.Services.AddScoped<IUserAuthenticationService, UserAuthenticationService>();

    // Registrace GraphServiceClient
    builder.Services.AddScoped(sp =>
    {
        var configuration = sp.GetRequiredService<IConfiguration>();
        var tenantId = configuration["MicrosoftGraph:TenantId"];
        var clientId = configuration["MicrosoftGraph:ClientId"];
        var clientSecret = configuration["MicrosoftGraph:ClientSecret"];
        
        var credentials = new ClientSecretCredential(tenantId, clientId, clientSecret);
        return new GraphServiceClient(credentials);
    });

    // Registrace služby pro konverzi dokumentů
    builder.Services.AddScoped<IDocumentConversionService, DocumentConversionService>();
    builder.Services.AddScoped<IDocumentConverter, GraphDocumentConverter>();
    builder.Services.AddScoped<ConversionSettings>();
    builder.Services.AddScoped<ConversionOptions>();

    builder.Services.AddAuthentication("BasicAuthentication")
        .AddScheme<AuthenticationSchemeOptions, BasicAuthenticationHandler>("BasicAuthentication", null);

    builder.Services.AddAuthorization();

    var app = builder.Build();

    string version = Assembly.GetExecutingAssembly().GetName().Version.Major.ToString() + "." + Assembly.GetExecutingAssembly().GetName().Version.Minor.ToString();
    string appName = app.Environment.ApplicationName;

    // Konfigurace pro generování OpenAPI 2.0 dokumentace
    app.UseSwagger(c =>
    {
        c.SerializeAsV2 = true;
        c.PreSerializeFilters.Add((swagger, httpReq) =>
        {
            swagger.Info.Title = appName;
            swagger.Info.Version = version;
            swagger.Servers = new List<OpenApiServer> { new OpenApiServer { Url = $"{httpReq.Scheme}://{httpReq.Host.Value}{httpReq.PathBase}" } };
        });
    });
    
    app.UseSwaggerUI(c => 
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", $"{appName} {version}");
        c.RoutePrefix = "swagger";
    });

    if (app.Environment.IsDevelopment())
    {
        app.UseDeveloperExceptionPage();
    }

    app.UseHttpsRedirection();
    app.UseRouting();
    app.UseAuthentication();
    app.UseAuthorization();

    app.MapDocumentConversionEndpoints();

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
